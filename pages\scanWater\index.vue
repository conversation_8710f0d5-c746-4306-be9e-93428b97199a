<template>
	<!-- 扫码用水 -->
	<view class="function-btn" @click="scanWater">
		<text class="iconfont">&#xe600;</text>
		<text class="btn-text">扫码用水</text>
	</view>

	<!-- H5扫码弹窗 -->
	<view v-if="showH5Scanner" class="scanner-modal" @click="closeScanner">
		<view class="scanner-container" @click.stop>
			<view class="scanner-header">
				<text class="scanner-title">扫描二维码</text>
				<text class="scanner-close" @click="closeScanner">×</text>
			</view>
			<view class="scanner-content">
				<view id="qr-reader" class="qr-reader"></view>
				<view class="scanner-tips">请将二维码放入框内进行扫描</view>
			</view>
		</view>
	</view>
</template>

<script setup>

import { ref, onMounted, nextTick } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// H5扫码相关状态
const showH5Scanner = ref(false)
let html5QrcodeScanner = null

// 生命周期钩子
onLoad(() => {
	// 原来的 onLoad 逻辑可以放在这里
})

// 开始扫码
const scanWater = () => {
	console.log('开始扫码')

	// 判断是否为H5环境
	// #ifdef H5
	startH5Scanner()
	// #endif

	// #ifndef H5
	uni.scanCode({
		success: async function (res) {
			console.log('条码内容：' + res.result);
			handleScanResult(res.result)
		},
		fail: function (err) {
			console.log("err", err);
		}
	});
	// #endif
}

// H5扫码功能
const startH5Scanner = async () => {
	showH5Scanner.value = true

	await nextTick()

	// 动态导入html5-qrcode
	try {
		const { Html5QrcodeScanner } = await import('html5-qrcode')

		html5QrcodeScanner = new Html5QrcodeScanner(
			"qr-reader",
			{
				fps: 10,
				qrbox: { width: 250, height: 250 },
				aspectRatio: 1.0
			},
			false
		)

		html5QrcodeScanner.render(onScanSuccess, onScanFailure)
	} catch (error) {
		console.error('加载扫码库失败:', error)
		uni.showToast({
			title: '扫码功能加载失败',
			icon: 'none'
		})
		closeScanner()
	}
}

// 扫码成功回调
const onScanSuccess = (decodedText, decodedResult) => {
	console.log('扫码成功:', decodedText)
	closeScanner()
	handleScanResult(decodedText)
}

// 扫码失败回调
const onScanFailure = (error) => {
	// 这里不需要处理，html5-qrcode会持续尝试扫码
}

// 关闭H5扫码器
const closeScanner = () => {
	if (html5QrcodeScanner) {
		html5QrcodeScanner.clear().catch(error => {
			console.error('清理扫码器失败:', error)
		})
		html5QrcodeScanner = null
	}
	showH5Scanner.value = false
}

// 处理扫码结果
const handleScanResult = (result) => {
	uni.showModal({
		title: '扫码成功',
		content: `扫码结果：${result}`,
		success: function (res) {
			if (res.confirm) {
				// 用户点击确定，可以进行下一步操作
				console.log('用户确认扫码结果')
			}
		}
	})
}

</script>
<style lang="scss" scoped>
.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}

/* H5扫码弹窗样式 */
.scanner-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.scanner-container {
	background-color: #fff;
	border-radius: 12px;
	width: 90%;
	max-width: 400px;
	max-height: 80%;
	overflow: hidden;
}

.scanner-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	border-bottom: 1px solid #eee;
	background-color: #f8f9fa;
}

.scanner-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.scanner-close {
	font-size: 24px;
	color: #666;
	cursor: pointer;
	width: 30px;
	height: 30px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;

	&:hover {
		background-color: #f0f0f0;
	}
}

.scanner-content {
	padding: 20px;
	text-align: center;
}

.qr-reader {
	width: 100%;
	max-width: 300px;
	margin: 0 auto;
}

.scanner-tips {
	margin-top: 16px;
	color: #666;
	font-size: 14px;
	line-height: 1.5;
}

/* 覆盖html5-qrcode默认样式 */
:deep(#qr-reader) {
	border: none !important;
}

:deep(#qr-reader__dashboard_section) {
	display: none !important;
}

:deep(#qr-reader__camera_selection) {
	margin-bottom: 10px !important;
}

:deep(#qr-reader__scan_region) {
	border-radius: 8px !important;
	overflow: hidden !important;
}

:deep(#qr-reader video) {
	border-radius: 8px !important;
}
</style>
